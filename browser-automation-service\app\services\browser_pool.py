"""
Browser Pool Manager
Manages a pool of browser instances for efficient resource utilization
"""

import asyncio
import sys
from typing import Dict, List, Optional, Any
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>rowserContext, Page
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.logging import LoggerMixin
from app.core.exceptions import BrowserUseException

# Fix for Windows asyncio issue with <PERSON><PERSON>
if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())


class BrowserInstance:
    """Represents a single browser instance in the pool"""
    
    def __init__(self, browser: Browser, instance_id: str):
        self.browser = browser
        self.instance_id = instance_id
        self.contexts: Dict[str, BrowserContext] = {}
        self.in_use = False
        self.created_at = asyncio.get_event_loop().time()
        self.last_used = self.created_at
    
    async def create_context(self, context_id: str, **kwargs) -> BrowserContext:
        """Create a new browser context"""
        context = await self.browser.new_context(
            viewport={
                'width': settings.BROWSER_VIEWPORT_WIDTH,
                'height': settings.BROWSER_VIEWPORT_HEIGHT
            },
            **kwargs
        )
        self.contexts[context_id] = context
        return context
    
    async def get_context(self, context_id: str) -> Optional[BrowserContext]:
        """Get an existing browser context"""
        return self.contexts.get(context_id)
    
    async def close_context(self, context_id: str):
        """Close a browser context"""
        if context_id in self.contexts:
            await self.contexts[context_id].close()
            del self.contexts[context_id]
    
    async def close(self):
        """Close the browser instance"""
        # Close all contexts
        for context in self.contexts.values():
            await context.close()
        self.contexts.clear()
        
        # Close browser
        await self.browser.close()


class BrowserPool(LoggerMixin):
    """Manages a pool of browser instances for efficient resource utilization"""
    
    def __init__(self):
        self.playwright = None
        self.browser_type = None
        self.instances: Dict[str, BrowserInstance] = {}
        self.available_instances: List[str] = []
        self.instance_counter = 0
        self._lock = asyncio.Lock()
    
    async def initialize(self):
        """Initialize the browser pool"""
        try:
            self.playwright = await async_playwright().start()
            self.browser_type = self.playwright.chromium
            
            # Pre-create initial browser instances
            for _ in range(min(2, settings.SESSION_POOL_SIZE)):
                await self._create_browser_instance()
            
            self.log_info(f"Browser pool initialized with {len(self.instances)} instances")
            
        except Exception as e:
            self.log_error(f"Failed to initialize browser pool: {e}")
            raise BrowserUseException(f"Browser pool initialization failed: {e}")
    
    async def cleanup(self):
        """Cleanup all browser instances"""
        try:
            # Close all browser instances
            for instance in self.instances.values():
                await instance.close()
            
            self.instances.clear()
            self.available_instances.clear()
            
            # Stop playwright
            if self.playwright:
                await self.playwright.stop()
            
            self.log_info("Browser pool cleanup completed")
            
        except Exception as e:
            self.log_error(f"Error during browser pool cleanup: {e}")
    
    @asynccontextmanager
    async def get_browser_context(self, session_id: str, **context_kwargs):
        """Get a browser context from the pool (context manager)"""
        instance = None
        context = None
        
        try:
            # Get browser instance
            instance = await self._get_available_instance()
            
            # Create context
            context = await instance.create_context(session_id, **context_kwargs)
            
            self.log_info(f"Acquired browser context for session: {session_id}")
            
            yield context
            
        except Exception as e:
            self.log_error(f"Error in browser context: {e}")
            raise BrowserUseException(f"Browser context error: {e}")
            
        finally:
            # Cleanup
            if instance and context:
                try:
                    await instance.close_context(session_id)
                    await self._release_instance(instance.instance_id)
                    self.log_info(f"Released browser context for session: {session_id}")
                except Exception as e:
                    self.log_error(f"Error releasing browser context: {e}")
    
    async def get_browser_instance(self, session_id: str) -> BrowserInstance:
        """Get a browser instance for direct use"""
        try:
            instance = await self._get_available_instance()
            self.log_info(f"Acquired browser instance for session: {session_id}")
            return instance
            
        except Exception as e:
            self.log_error(f"Failed to get browser instance: {e}")
            raise BrowserUseException(f"Browser instance acquisition failed: {e}")
    
    async def release_browser_instance(self, instance_id: str):
        """Release a browser instance back to the pool"""
        await self._release_instance(instance_id)
    
    async def _get_available_instance(self) -> BrowserInstance:
        """Get an available browser instance from the pool"""
        async with self._lock:
            # Check for available instances
            if self.available_instances:
                instance_id = self.available_instances.pop(0)
                instance = self.instances[instance_id]
                instance.in_use = True
                instance.last_used = asyncio.get_event_loop().time()
                return instance
            
            # Create new instance if under limit
            if len(self.instances) < settings.SESSION_POOL_SIZE:
                return await self._create_browser_instance()
            
            # Wait for an instance to become available
            max_wait_time = 30.0  # 30 seconds max wait
            wait_interval = 0.5   # Check every 500ms
            waited_time = 0.0

            while waited_time < max_wait_time:
                await asyncio.sleep(wait_interval)
                waited_time += wait_interval

                # Check if any instance became available
                if self.available_instances:
                    instance_id = self.available_instances.pop(0)
                    instance = self.instances[instance_id]
                    instance.in_use = True
                    instance.last_used = asyncio.get_event_loop().time()
                    return instance

                # Check if we can create a new instance
                if len(self.instances) < settings.SESSION_POOL_SIZE:
                    return await self._create_browser_instance()

            # If we've waited too long, raise exception
            raise BrowserUseException(f"No browser instances available after waiting {max_wait_time} seconds")
    
    async def _release_instance(self, instance_id: str):
        """Release a browser instance back to the pool"""
        async with self._lock:
            if instance_id in self.instances:
                instance = self.instances[instance_id]
                instance.in_use = False
                instance.last_used = asyncio.get_event_loop().time()

                # Add back to available pool
                if instance_id not in self.available_instances:
                    self.available_instances.append(instance_id)

                self.log_info(f"Released browser instance: {instance_id}")

    async def release_browser_instance(self, instance_id: str):
        """Public method to release a browser instance back to the pool"""
        await self._release_instance(instance_id)
    
    async def _create_browser_instance(self) -> BrowserInstance:
        """Create a new browser instance"""
        try:
            self.instance_counter += 1
            instance_id = f"browser_{self.instance_counter}"
            
            # Launch browser
            browser = await self.browser_type.launch(
                headless=settings.BROWSER_HEADLESS,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            )
            
            # Create browser instance
            instance = BrowserInstance(browser, instance_id)
            instance.in_use = True  # Mark as in use initially
            
            # Store in pool
            self.instances[instance_id] = instance
            
            self.log_info(f"Created browser instance: {instance_id}")
            return instance
            
        except Exception as e:
            self.log_error(f"Failed to create browser instance: {e}")
            raise BrowserUseException(f"Browser instance creation failed: {e}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check for browser pool"""
        try:
            total_instances = len(self.instances)
            available_instances = len(self.available_instances)
            in_use_instances = total_instances - available_instances
            
            return {
                "total_instances": total_instances,
                "available_instances": available_instances,
                "in_use_instances": in_use_instances,
                "max_instances": settings.SESSION_POOL_SIZE,
                "status": "healthy"
            }
            
        except Exception as e:
            self.log_error(f"Browser pool health check failed: {e}")
            return {
                "error": str(e),
                "status": "unhealthy"
            }
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get detailed statistics about the browser pool"""
        current_time = asyncio.get_event_loop().time()
        
        stats = {
            "total_instances": len(self.instances),
            "available_instances": len(self.available_instances),
            "in_use_instances": len([i for i in self.instances.values() if i.in_use]),
            "instance_details": []
        }
        
        for instance_id, instance in self.instances.items():
            stats["instance_details"].append({
                "instance_id": instance_id,
                "in_use": instance.in_use,
                "contexts": len(instance.contexts),
                "age_seconds": current_time - instance.created_at,
                "idle_seconds": current_time - instance.last_used
            })
        
        return stats


# Global browser pool instance
browser_pool = BrowserPool()
