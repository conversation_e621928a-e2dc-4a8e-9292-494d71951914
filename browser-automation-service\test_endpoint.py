#!/usr/bin/env python3
"""
Quick test to check if the browser automation endpoint is available
"""

import asyncio
import httpx

async def test_endpoints():
    """Test available endpoints"""
    
    async with httpx.AsyncClient() as client:
        # Test health endpoint
        try:
            response = await client.get("http://localhost:8001/health", timeout=5)
            print(f"✅ Health endpoint: {response.status_code}")
        except Exception as e:
            print(f"❌ Health endpoint failed: {e}")
        
        # Test docs endpoint
        try:
            response = await client.get("http://localhost:8001/docs", timeout=5)
            print(f"✅ Docs endpoint: {response.status_code}")
        except Exception as e:
            print(f"❌ Docs endpoint failed: {e}")
        
        # Test the browser automation endpoint
        try:
            response = await client.post(
                "http://localhost:8001/api/v1/browser/execute",
                json={"task": "test"},
                timeout=5
            )
            print(f"✅ Browser automation endpoint: {response.status_code}")
            if response.status_code != 200:
                print(f"Response: {response.text}")
        except Exception as e:
            print(f"❌ Browser automation endpoint failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_endpoints())
