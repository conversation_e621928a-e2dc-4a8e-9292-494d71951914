import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function PUT(
  request: NextRequest,
  { params }: { params: { configId: string } }
) {
  try {
    const cookieStore = cookies();
    const authCookie = cookieStore.get('sb-access-token');
    
    if (!authCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { configId } = params;
    const { enabled } = await request.json();

    if (typeof enabled !== 'boolean') {
      return NextResponse.json({ error: 'Invalid enabled value' }, { status: 400 });
    }

    // Get user from auth cookie
    const { data: { user }, error: authError } = await supabase.auth.getUser(authCookie.value);
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user tier to validate access
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('tier')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    const userTier = subscription?.tier || 'free';

    // Check if user has access to browser automation
    // Allow starter, professional, and enterprise tiers
    const allowedTiers = ['starter', 'professional', 'enterprise'];
    if (enabled && !allowedTiers.includes(userTier)) {
      return NextResponse.json(
        { error: 'Browser automation requires Starter plan or higher' },
        { status: 403 }
      );
    }

    // Verify the config belongs to the user and update browser automation setting
    const { data: config, error: updateError } = await supabase
      .from('custom_api_configs')
      .update({ browser_automation_enabled: enabled })
      .eq('id', configId)
      .eq('user_id', user.id)
      .select('id, browser_automation_enabled')
      .single();

    if (updateError || !config) {
      if (updateError?.code === 'PGRST116') {
        return NextResponse.json({ error: 'Configuration not found' }, { status: 404 });
      }
      return NextResponse.json({ error: 'Failed to update browser automation setting' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      enabled: config.browser_automation_enabled,
      message: `Browser automation ${enabled ? 'enabled' : 'disabled'} successfully`
    });

  } catch (error) {
    console.error('Error updating browser automation setting:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
