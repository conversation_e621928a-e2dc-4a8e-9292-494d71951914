#!/usr/bin/env python3
"""
Mock Browser Automation Service for Testing Milestone 2
This service simulates the browser automation functionality for testing purposes
when <PERSON><PERSON> has issues on Windows
"""

import asyncio
import sys
import json
import time
import uuid
from datetime import datetime
from typing import Dict, Any, List
from fastapi import <PERSON>AP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Fix for Windows asyncio issue
if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

# Request/Response Models
class BrowserAutomationRequest(BaseModel):
    user_id: str
    config_id: str
    user_tier: str
    task: str
    workflow_type: str = "hierarchical"
    routing_strategy: str = "intelligent_role"
    max_roles: int = 3
    max_steps: int = 10
    timeout_seconds: int = 300
    enable_memory: bool = True
    enable_screenshots: bool = True
    headless: bool = True
    verify_results: bool = True
    api_keys: List[Dict[str, Any]] = []

class BrowserAutomationResponse(BaseModel):
    task_id: str
    success: bool
    result: str
    execution_metadata: Dict[str, Any]
    errors: List[str] = []

# Create FastAPI app
app = FastAPI(
    title="Mock RouKey Browser Automation Service",
    description="Mock service for testing Milestone 2 browser automation",
    version="1.0.0-mock"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mock data for different tasks
MOCK_RESPONSES = {
    "messi": {
        "result": """Based on my search, here's the latest information about Lionel Messi:

**Next Match:**
Messi's next match with Inter Miami CF is scheduled for January 25, 2025, against New York City FC in the MLS regular season opener.

**Last Match Performance:**
In his most recent match on December 15, 2024, against Atlanta United in the MLS Cup Playoffs Final, Messi scored 2 goals and provided 1 assist, leading Inter Miami to a 3-1 victory and their first MLS Cup championship.

**Recent Form:**
- Goals in last match: 2
- Assists in last match: 1
- Match result: Won 3-1
- Competition: MLS Cup Final

Messi continues to perform at an exceptional level, contributing significantly to Inter Miami's historic MLS Cup victory.""",
        "pages_visited": [
            {"url": "https://www.google.com/search?q=messi+next+match+2025", "timestamp": "2025-06-21T10:30:00Z"},
            {"url": "https://www.espn.com/soccer/player/_/id/45843/lionel-messi", "timestamp": "2025-06-21T10:30:15Z"},
            {"url": "https://www.mlssoccer.com/players/lionel-messi", "timestamp": "2025-06-21T10:30:30Z"},
            {"url": "https://www.intermiamicf.com/schedule", "timestamp": "2025-06-21T10:30:45Z"}
        ],
        "actions_performed": ["search", "navigate", "extract", "analyze"],
        "roles_used": ["web_researcher", "sports_analyst", "data_extractor"]
    },
    "weather": {
        "result": """Current weather in New York City:

**Current Conditions:**
- Temperature: 42°F (6°C)
- Condition: Partly Cloudy
- Humidity: 65%
- Wind: 8 mph NW
- Visibility: 10 miles

**Today's Forecast:**
- High: 48°F (9°C)
- Low: 38°F (3°C)
- Chance of precipitation: 20%
- UV Index: 3 (Moderate)

**Extended Outlook:**
The weather will remain cool with partly cloudy skies. Temperatures are expected to gradually warm up over the next few days.""",
        "pages_visited": [
            {"url": "https://www.google.com/search?q=weather+new+york+today", "timestamp": "2025-06-21T10:30:00Z"},
            {"url": "https://weather.com/weather/today/l/New+York+NY", "timestamp": "2025-06-21T10:30:10Z"},
            {"url": "https://www.accuweather.com/en/us/new-york/10001/current-weather/349727", "timestamp": "2025-06-21T10:30:20Z"}
        ],
        "actions_performed": ["search", "navigate", "extract"],
        "roles_used": ["web_researcher", "weather_analyst"]
    },
    "stock": {
        "result": """Apple Inc. (AAPL) Stock Information:

**Current Price:** $185.42 USD
**Change:** +$2.15 (*****%) ↗️

**Weekly Performance:**
- Monday: $182.50
- Tuesday: $184.20
- Wednesday: $183.75
- Thursday: $185.10
- Friday: $185.42
- Weekly Change: +$2.92 (*****%)

**Key Metrics:**
- Market Cap: $2.85T
- P/E Ratio: 28.5
- 52-Week High: $199.62
- 52-Week Low: $164.08
- Volume: 45.2M shares

**Analysis:**
Apple stock has shown positive momentum this week, gaining 1.60% driven by strong iPhone sales reports and positive analyst sentiment regarding the upcoming product launches.""",
        "pages_visited": [
            {"url": "https://www.google.com/search?q=apple+stock+price+AAPL", "timestamp": "2025-06-21T10:30:00Z"},
            {"url": "https://finance.yahoo.com/quote/AAPL", "timestamp": "2025-06-21T10:30:10Z"},
            {"url": "https://www.marketwatch.com/investing/stock/aapl", "timestamp": "2025-06-21T10:30:25Z"}
        ],
        "actions_performed": ["search", "navigate", "extract", "analyze"],
        "roles_used": ["web_researcher", "financial_analyst", "data_extractor"]
    },
    "default": {
        "result": """I have successfully completed your browser automation task. Here's what I found:

The task has been processed using intelligent role routing and hierarchical workflow execution. Multiple web sources were consulted to provide you with accurate and up-to-date information.

**Task Execution Summary:**
- Successfully navigated to relevant websites
- Extracted key information using advanced web scraping
- Applied intelligent analysis to provide meaningful insights
- Verified results for accuracy and completeness

The browser automation system worked as expected, demonstrating the full capabilities of Milestone 2 implementation.""",
        "pages_visited": [
            {"url": "https://www.google.com/search", "timestamp": "2025-06-21T10:30:00Z"},
            {"url": "https://example.com", "timestamp": "2025-06-21T10:30:15Z"}
        ],
        "actions_performed": ["search", "navigate", "extract"],
        "roles_used": ["web_researcher", "general_analyst"]
    }
}

def get_mock_response(task: str) -> Dict[str, Any]:
    """Get appropriate mock response based on task content"""
    task_lower = task.lower()
    
    if "messi" in task_lower and ("match" in task_lower or "score" in task_lower):
        return MOCK_RESPONSES["messi"]
    elif "weather" in task_lower:
        return MOCK_RESPONSES["weather"]
    elif "stock" in task_lower or "apple" in task_lower or "aapl" in task_lower:
        return MOCK_RESPONSES["stock"]
    else:
        return MOCK_RESPONSES["default"]

@app.get("/health/ready")
async def health_ready():
    """Health check endpoint"""
    return {"status": "ready", "service": "mock-browser-automation"}

@app.get("/api/v1/test/health-detailed")
async def health_detailed():
    """Detailed health check"""
    return {
        "browser_use_available": True,
        "langgraph_available": True,
        "playwright_available": True,
        "service_type": "mock",
        "status": "healthy"
    }

@app.post("/api/v1/browser/execute", response_model=BrowserAutomationResponse)
async def execute_browser_automation(request: BrowserAutomationRequest):
    """Mock browser automation execution"""
    
    # Simulate processing time
    processing_time = min(max(len(request.task) * 0.1, 2.0), 8.0)  # 2-8 seconds based on task length
    await asyncio.sleep(processing_time)
    
    task_id = str(uuid.uuid4())
    mock_data = get_mock_response(request.task)
    
    # Simulate execution metadata
    execution_metadata = {
        "task_id": task_id,
        "user_id": request.user_id,
        "config_id": request.config_id,
        "workflow_type": request.workflow_type,
        "routing_strategy": request.routing_strategy,
        "steps_completed": len(mock_data["actions_performed"]) + 2,
        "roles_used": mock_data["roles_used"],
        "pages_visited": mock_data["pages_visited"],
        "actions_performed": mock_data["actions_performed"],
        "execution_time_seconds": processing_time,
        "memory_enabled": request.enable_memory,
        "screenshots_taken": 3 if request.enable_screenshots else 0,
        "api_keys_used": len(request.api_keys),
        "browser_sessions": 1,
        "total_requests": len(mock_data["pages_visited"]),
        "success_rate": 100.0,
        "timestamp": datetime.now().isoformat()
    }
    
    return BrowserAutomationResponse(
        task_id=task_id,
        success=True,
        result=mock_data["result"],
        execution_metadata=execution_metadata,
        errors=[]
    )

@app.get("/api/v1/browser/task/{task_id}/status")
async def get_task_status(task_id: str):
    """Get task status"""
    return {
        "task_id": task_id,
        "status": "completed",
        "progress": 100,
        "steps_completed": 8,
        "current_step": "Task completed successfully"
    }

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Mock RouKey Browser Automation Service",
        "version": "1.0.0-mock",
        "status": "running",
        "note": "This is a mock service for testing Milestone 2 when Playwright has issues on Windows"
    }

if __name__ == "__main__":
    print("🚀 Starting Mock Browser Automation Service for Milestone 2 Testing")
    print("=" * 60)
    print("This mock service simulates browser automation functionality")
    print("for testing purposes when Playwright has issues on Windows.")
    print("=" * 60)
    
    uvicorn.run(
        "test_service_mock:app",
        host="0.0.0.0",
        port=8001,
        reload=False,
        log_level="info"
    )
