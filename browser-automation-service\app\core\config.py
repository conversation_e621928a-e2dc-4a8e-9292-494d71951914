"""
Configuration settings for the Browser Automation Service
"""

import os
from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # Service Configuration
    SERVICE_HOST: str = Field(default="localhost", env="SERVICE_HOST")
    SERVICE_PORT: int = Field(default=8000, env="SERVICE_PORT")
    SERVICE_ENV: str = Field(default="development", env="SERVICE_ENV")
    DEBUG: bool = Field(default=True, env="DEBUG")
    
    # RouKey Integration
    ROKEY_API_URL: str = Field(default="http://localhost:3000", env="ROKEY_API_URL")
    ROKEY_API_SECRET: str = Field(default="", env="ROKEY_API_SECRET")
    
    # Database Configuration (Supabase)
    DATABASE_URL: str = Field(default="", env="DATABASE_URL")
    SUPABASE_URL: str = Field(default="", env="SUPABASE_URL")
    SUPABASE_SERVICE_ROLE_KEY: str = Field(default="", env="SUPABASE_SERVICE_ROLE_KEY")
    
    # Redis Configuration
    REDIS_URL: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    REDIS_SESSION_DB: int = Field(default=1, env="REDIS_SESSION_DB")
    REDIS_CACHE_DB: int = Field(default=2, env="REDIS_CACHE_DB")
    
    # LLM API Keys
    OPENAI_API_KEY: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    ANTHROPIC_API_KEY: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    GOOGLE_API_KEY: Optional[str] = Field(default=None, env="GOOGLE_API_KEY")
    DEEPSEEK_API_KEY: Optional[str] = Field(default=None, env="DEEPSEEK_API_KEY")
    GROK_API_KEY: Optional[str] = Field(default=None, env="GROK_API_KEY")
    
    # Google Custom Search API
    GOOGLE_SEARCH_API_KEY: Optional[str] = Field(default=None, env="GOOGLE_SEARCH_API_KEY")
    GOOGLE_SEARCH_ENGINE_ID: Optional[str] = Field(default=None, env="GOOGLE_SEARCH_ENGINE_ID")
    GOOGLE_CSE_ID: Optional[str] = Field(default=None, env="GOOGLE_CSE_ID")

    # Classification API
    ROKEY_CLASSIFICATION_GEMINI_API_KEY: Optional[str] = Field(default=None, env="ROKEY_CLASSIFICATION_GEMINI_API_KEY")
    
    # Memory Configuration (Qdrant)
    QDRANT_HOST: str = Field(default="localhost", env="QDRANT_HOST")
    QDRANT_PORT: int = Field(default=6333, env="QDRANT_PORT")
    QDRANT_API_KEY: Optional[str] = Field(default=None, env="QDRANT_API_KEY")
    QDRANT_COLLECTION_NAME: str = Field(default="browser_automation_memory", env="QDRANT_COLLECTION_NAME")
    
    # Browser Configuration
    BROWSER_HEADLESS: bool = Field(default=True, env="BROWSER_HEADLESS")
    BROWSER_TIMEOUT: int = Field(default=30000, env="BROWSER_TIMEOUT")
    BROWSER_VIEWPORT_WIDTH: int = Field(default=1920, env="BROWSER_VIEWPORT_WIDTH")
    BROWSER_VIEWPORT_HEIGHT: int = Field(default=1080, env="BROWSER_VIEWPORT_HEIGHT")
    BROWSER_SLOW_MO: int = Field(default=0, env="BROWSER_SLOW_MO")
    BROWSER_DEVTOOLS: bool = Field(default=False, env="BROWSER_DEVTOOLS")
    
    # Session Management
    SESSION_POOL_SIZE: int = Field(default=10, env="SESSION_POOL_SIZE")
    SESSION_TIMEOUT: int = Field(default=300, env="SESSION_TIMEOUT")
    MAX_CONCURRENT_SESSIONS: int = Field(default=5, env="MAX_CONCURRENT_SESSIONS")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(default="json", env="LOG_FORMAT")
    
    # Security
    JWT_SECRET_KEY: str = Field(default="your-secret-key", env="JWT_SECRET_KEY")
    CORS_ORIGINS: str = Field(
        default="http://localhost:3000",
        env="CORS_ORIGINS"
    )
    
    # Performance
    MAX_WORKERS: int = Field(default=4, env="MAX_WORKERS")
    REQUEST_TIMEOUT: int = Field(default=300, env="REQUEST_TIMEOUT")
    RATE_LIMIT_PER_MINUTE: int = Field(default=60, env="RATE_LIMIT_PER_MINUTE")

    # User Tier Configuration
    USER_TIERS: str = Field(default="free,starter,pro,professional,enterprise", env="USER_TIERS")
    TIER_BROWSER_AUTOMATION_LIMITS: str = Field(default="0,15,100,100,1000", env="TIER_BROWSER_AUTOMATION_LIMITS")
    TIER_BROWSER_AUTOMATION_ACCESS: str = Field(default="false,true,true,true,true", env="TIER_BROWSER_AUTOMATION_ACCESS")
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Parse CORS_ORIGINS string into list
        if isinstance(self.CORS_ORIGINS, str):
            self.cors_origins_list = [origin.strip() for origin in self.CORS_ORIGINS.split(",")]
        else:
            self.cors_origins_list = ["http://localhost:3000"]

        # Parse tier configuration
        self._parse_tier_config()

    @property
    def cors_origins_list(self) -> list:
        """Get CORS origins as a list"""
        if hasattr(self, '_cors_origins_list'):
            return self._cors_origins_list
        return ["http://localhost:3000"]

    @cors_origins_list.setter
    def cors_origins_list(self, value: list):
        """Set CORS origins list"""
        self._cors_origins_list = value

    def _parse_tier_config(self):
        """Parse tier configuration from environment variables"""
        try:
            # Parse tiers
            self.user_tiers = [tier.strip() for tier in self.USER_TIERS.split(",")]

            # Parse limits
            limits_str = [limit.strip() for limit in self.TIER_BROWSER_AUTOMATION_LIMITS.split(",")]
            self.tier_limits = [int(limit) for limit in limits_str]

            # Parse access
            access_str = [access.strip().lower() for access in self.TIER_BROWSER_AUTOMATION_ACCESS.split(",")]
            self.tier_access = [access == "true" for access in access_str]

            # Create tier mapping
            self.tier_config = {}
            for i, tier in enumerate(self.user_tiers):
                self.tier_config[tier] = {
                    "browser_automation_limit": self.tier_limits[i] if i < len(self.tier_limits) else 0,
                    "browser_automation_access": self.tier_access[i] if i < len(self.tier_access) else False
                }

        except Exception as e:
            # Fallback to default configuration
            self.tier_config = {
                "free": {"browser_automation_limit": 0, "browser_automation_access": False},
                "starter": {"browser_automation_limit": 15, "browser_automation_access": True},
                "pro": {"browser_automation_limit": 100, "browser_automation_access": True},
                "professional": {"browser_automation_limit": 100, "browser_automation_access": True},
                "enterprise": {"browser_automation_limit": 1000, "browser_automation_access": True}
            }

    def get_tier_browser_automation_limit(self, tier: str) -> int:
        """Get browser automation limit for a tier"""
        return self.tier_config.get(tier, {}).get("browser_automation_limit", 0)

    def has_browser_automation_access(self, tier: str) -> bool:
        """Check if tier has browser automation access"""
        return self.tier_config.get(tier, {}).get("browser_automation_access", False)

    def get_all_tiers(self) -> List[str]:
        """Get list of all available tiers"""
        return list(self.tier_config.keys())

    def is_valid_tier(self, tier: str) -> bool:
        """Check if tier is valid"""
        return tier in self.tier_config


# Global settings instance
settings = Settings()
