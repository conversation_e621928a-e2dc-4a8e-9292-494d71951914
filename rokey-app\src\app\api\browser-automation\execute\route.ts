import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const BROWSER_AUTOMATION_SERVICE_URL = process.env.BROWSER_AUTOMATION_SERVICE_URL || 'http://localhost:8001';

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const authCookie = cookieStore.get('sb-access-token');
    
    if (!authCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from auth cookie
    const { data: { user }, error: authError } = await supabase.auth.getUser(authCookie.value);
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { task, task_type = 'navigation', config_id, extracted_parameters = {}, user_tier, stream = false } = body;

    if (!task || !config_id) {
      return NextResponse.json({ error: 'Task and config_id are required' }, { status: 400 });
    }

    // Verify the config belongs to the user and has browser automation enabled
    const { data: config, error: configError } = await supabase
      .from('custom_api_configs')
      .select('id, user_id, browser_automation_enabled, name')
      .eq('id', config_id)
      .eq('user_id', user.id)
      .single();

    if (configError || !config) {
      return NextResponse.json({ error: 'Configuration not found' }, { status: 404 });
    }

    if (!config.browser_automation_enabled) {
      return NextResponse.json({ error: 'Browser automation is not enabled for this configuration' }, { status: 403 });
    }

    // Get user tier and validate access
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('tier')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    const userTier = subscription?.tier || 'free';

    // Allow starter, professional, and enterprise tiers
    const allowedTiers = ['starter', 'professional', 'enterprise'];
    if (!allowedTiers.includes(userTier)) {
      return NextResponse.json({ error: 'Browser automation requires Starter plan or higher' }, { status: 403 });
    }

    // Check and enforce quota limits
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format

    // Get or create usage record for current month
    let { data: usage, error: usageError } = await supabase
      .from('browser_automation_usage')
      .select('*')
      .eq('user_id', user.id)
      .eq('month_year', currentMonth)
      .single();

    if (usageError && usageError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking browser automation usage:', usageError);
      return NextResponse.json({ error: 'Failed to check usage quota' }, { status: 500 });
    }

    // Define tier limits
    const tierLimits = {
      free: 0,
      starter: 15,
      pro: 100,
      enterprise: 1000
    };

    const monthlyLimit = tierLimits[userTier as keyof typeof tierLimits] || 0;

    if (!usage) {
      // Create new usage record
      const { data: newUsage, error: createError } = await supabase
        .from('browser_automation_usage')
        .insert({
          user_id: user.id,
          month_year: currentMonth,
          tier: userTier,
          tasks_used: 0,
          tasks_limit: monthlyLimit
        })
        .select()
        .single();

      if (createError) {
        console.error('Error creating usage record:', createError);
        return NextResponse.json({ error: 'Failed to initialize usage tracking' }, { status: 500 });
      }
      usage = newUsage;
    }

    // Check if user has exceeded quota
    if (usage.tasks_used >= usage.tasks_limit) {
      return NextResponse.json({
        error: `Monthly browser automation quota exceeded. You have used ${usage.tasks_used}/${usage.tasks_limit} tasks this month.`,
        quota_exceeded: true,
        tasks_used: usage.tasks_used,
        tasks_limit: usage.tasks_limit,
        upgrade_required: userTier === 'starter' ? 'pro' : null
      }, { status: 429 });
    }

    // Get user's API keys and roles for this config
    const { data: apiKeys, error: keysError } = await supabase
      .from('api_keys')
      .select(`
        id,
        provider,
        predefined_model_id,
        temperature,
        label,
        api_key_encrypted,
        key_role_assignments (
          role_name
        )
      `)
      .eq('custom_api_config_id', config_id)
      .eq('status', 'active');

    if (keysError || !apiKeys || apiKeys.length === 0) {
      return NextResponse.json({ error: 'No active API keys found for this configuration' }, { status: 400 });
    }

    // Prepare the request for the browser automation service
    // This integrates with RouKey's BYOK system - user's API keys and role assignments
    const browserAutomationRequest = {
      task,
      task_type,
      user_id: user.id,
      config_id,
      config_name: config.name,
      user_tier: userTier,
      extracted_parameters,
      // Pass user's BYOK API keys with role assignments for intelligent routing
      api_keys: apiKeys.map(key => ({
        id: key.id,
        provider: key.provider,
        model: key.predefined_model_id,
        api_key: key.api_key, // User's actual API key from BYOK
        temperature: key.temperature,
        label: key.label,
        roles: key.key_role_assignments?.map((assignment: any) => assignment.role_name) || [],
        // Browser automation will use these keys for LLM calls via role routing
        routing_strategy: config.routing_strategy
      })),
      // Browser configuration options (user configurable)
      browser_headless: extracted_parameters.headless ?? true,
      browser_viewport_width: extracted_parameters.viewport_width ?? 1920,
      browser_viewport_height: extracted_parameters.viewport_height ?? 1080,
      browser_slow_mo: extracted_parameters.slow_mo ?? 0,
      browser_devtools: extracted_parameters.devtools ?? false,
      stream
    };

    // Call the browser automation service
    const endpoint = stream ? '/api/v1/browser/execute/stream' : '/api/v1/browser/execute';
    const response = await fetch(`${BROWSER_AUTOMATION_SERVICE_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(browserAutomationRequest)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Browser automation service error' }));
      return NextResponse.json(
        { error: errorData.error || 'Browser automation service failed' },
        { status: response.status }
      );
    }

    // If streaming, return the stream
    if (stream) {
      return new NextResponse(response.body, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      });
    }

    // For non-streaming, return the JSON response
    const result = await response.json();

    // Consume quota after successful execution
    if (result.success !== false) { // Only consume if not explicitly failed
      try {
        await supabase
          .from('browser_automation_usage')
          .update({
            tasks_used: usage.tasks_used + 1,
            last_task_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('user_id', user.id)
          .eq('month_year', currentMonth);

        // Log analytics
        await supabase
          .from('browser_automation_analytics')
          .insert({
            user_id: user.id,
            task_id: result.task_id,
            event_type: 'task_completed',
            event_data: {
              task_type,
              extracted_parameters,
              execution_time_ms: result.execution_metadata?.execution_time_seconds ? result.execution_metadata.execution_time_seconds * 1000 : null,
              steps_completed: result.execution_metadata?.steps_completed
            },
            tier: userTier,
            config_id,
            success: result.success,
            execution_time_ms: result.execution_metadata?.execution_time_seconds ? result.execution_metadata.execution_time_seconds * 1000 : null
          });

        console.log(`[Browser Automation] Quota consumed for user ${user.id}. Usage: ${usage.tasks_used + 1}/${usage.tasks_limit}`);
      } catch (quotaError) {
        console.error('Error updating quota:', quotaError);
        // Don't fail the request if quota update fails
      }
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error('Error executing browser automation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
