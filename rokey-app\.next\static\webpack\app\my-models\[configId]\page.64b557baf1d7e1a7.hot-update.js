"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/app/my-models/[configId]/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/my-models/[configId]/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConfigDetailsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _config_models__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/models */ \"(app-pages-browser)/./src/config/models.ts\");\n/* harmony import */ var _config_roles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/roles */ \"(app-pages-browser)/./src/config/roles.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var react_tooltip__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-tooltip */ \"(app-pages-browser)/./node_modules/react-tooltip/dist/react-tooltip.min.mjs\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _hooks_useManageKeysPrefetch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useManageKeysPrefetch */ \"(app-pages-browser)/./src/hooks/useManageKeysPrefetch.ts\");\n/* harmony import */ var _components_ManageKeysLoadingSkeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ManageKeysLoadingSkeleton */ \"(app-pages-browser)/./src/components/ManageKeysLoadingSkeleton.tsx\");\n/* harmony import */ var _hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useRoutingSetupPrefetch */ \"(app-pages-browser)/./src/hooks/useRoutingSetupPrefetch.ts\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n // For accessing route params\n // Ensure path is correct\n\n\n\n\n\n\n\n\n\n// Updated: PROVIDER_OPTIONS uses p.id (slug) for value and p.name for label\nconst PROVIDER_OPTIONS = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.map(_c = (p)=>({\n        value: p.id,\n        label: p.name\n    }));\n_c1 = PROVIDER_OPTIONS;\nfunction ConfigDetailsPage() {\n    var _PROVIDER_OPTIONS_, _llmProviders_find;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const configId = params.configId;\n    // Confirmation modal hook\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_7__.useConfirmation)();\n    // Navigation hook with safe context\n    const navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_11__.useNavigationSafe)();\n    const navigateOptimistically = (navigationContext === null || navigationContext === void 0 ? void 0 : navigationContext.navigateOptimistically) || ((href)=>{\n        window.location.href = href;\n    });\n    // Prefetch hooks\n    const { getCachedData, isCached } = (0,_hooks_useManageKeysPrefetch__WEBPACK_IMPORTED_MODULE_8__.useManageKeysPrefetch)();\n    const { createHoverPrefetch: createRoutingHoverPrefetch } = (0,_hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_10__.useRoutingSetupPrefetch)();\n    const [configDetails, setConfigDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingConfig, setIsLoadingConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showOptimisticLoading, setShowOptimisticLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Provider state now stores the slug (p.id)\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(((_PROVIDER_OPTIONS_ = PROVIDER_OPTIONS[0]) === null || _PROVIDER_OPTIONS_ === void 0 ? void 0 : _PROVIDER_OPTIONS_.value) || 'openai'); // Stores slug\n    const [predefinedModelId, setPredefinedModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [apiKeyRaw, setApiKeyRaw] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [label, setLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [temperature, setTemperature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.0);\n    const [isSavingKey, setIsSavingKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for dynamic model fetching\n    const [fetchedProviderModels, setFetchedProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFetchingProviderModels, setIsFetchingProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fetchProviderModelsError, setFetchProviderModelsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [savedKeysWithRoles, setSavedKeysWithRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingKeysAndRoles, setIsLoadingKeysAndRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDeletingKey, setIsDeletingKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [defaultGeneralChatKeyId, setDefaultGeneralChatKeyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingRolesApiKey, setEditingRolesApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for editing API keys\n    const [editingApiKey, setEditingApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editTemperature, setEditTemperature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.0);\n    const [editPredefinedModelId, setEditPredefinedModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSavingEdit, setIsSavingEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State for User-Defined Custom Roles\n    const [userCustomRoles, setUserCustomRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingUserCustomRoles, setIsLoadingUserCustomRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userCustomRolesError, setUserCustomRolesError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreateCustomRoleForm, setShowCreateCustomRoleForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newCustomRoleId, setNewCustomRoleId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newCustomRoleName, setNewCustomRoleName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newCustomRoleDescription, setNewCustomRoleDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSavingCustomRole, setIsSavingCustomRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createCustomRoleError, setCreateCustomRoleError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingCustomRoleId, setDeletingCustomRoleId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // stores the DB ID (UUID) of the custom role\n    // Browser Automation state\n    const [browserAutomationEnabled, setBrowserAutomationEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [browserAutomationLoading, setBrowserAutomationLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [browserAutomationError, setBrowserAutomationError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userTier, setUserTier] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('free');\n    // Fetch config details with optimistic loading\n    const fetchConfigDetails = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchConfigDetails]\": async ()=>{\n            if (!configId) return;\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.configDetails) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached config data for: \".concat(configId));\n                setConfigDetails(cachedData.configDetails);\n                setIsLoadingConfig(false);\n                return;\n            }\n            // Show optimistic loading for first-time visits\n            if (!isCached(configId)) {\n                setShowOptimisticLoading(true);\n            }\n            setIsLoadingConfig(true);\n            setError(null);\n            try {\n                const res = await fetch(\"/api/custom-configs\");\n                if (!res.ok) {\n                    const errData = await res.json();\n                    throw new Error(errData.error || 'Failed to fetch configurations list');\n                }\n                const allConfigs = await res.json();\n                const currentConfig = allConfigs.find({\n                    \"ConfigDetailsPage.useCallback[fetchConfigDetails].currentConfig\": (c)=>c.id === configId\n                }[\"ConfigDetailsPage.useCallback[fetchConfigDetails].currentConfig\"]);\n                if (!currentConfig) throw new Error('Configuration not found in the list.');\n                setConfigDetails(currentConfig);\n            } catch (err) {\n                setError(\"Error loading model configuration: \".concat(err.message));\n                setConfigDetails(null);\n            } finally{\n                setIsLoadingConfig(false);\n                setShowOptimisticLoading(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchConfigDetails]\"], [\n        configId,\n        getCachedData,\n        isCached\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            fetchConfigDetails();\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        fetchConfigDetails\n    ]);\n    // New: Function to fetch all models from the database with caching\n    const fetchModelsFromDatabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchModelsFromDatabase]\": async ()=>{\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.models) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached models data for: \".concat(configId));\n                setFetchedProviderModels(cachedData.models);\n                setIsFetchingProviderModels(false);\n                return;\n            }\n            setIsFetchingProviderModels(true);\n            setFetchProviderModelsError(null);\n            setFetchedProviderModels(null);\n            try {\n                // The new API doesn't need a specific provider or API key in the body to list models\n                const response = await fetch('/api/providers/list-models', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({})\n                });\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || 'Failed to fetch models from database.');\n                }\n                if (data.models) {\n                    setFetchedProviderModels(data.models);\n                } else {\n                    setFetchedProviderModels([]);\n                }\n            } catch (err) {\n                setFetchProviderModelsError(\"Error fetching models: \".concat(err.message));\n                setFetchedProviderModels([]); // Set to empty array on error to prevent blocking UI\n            } finally{\n                setIsFetchingProviderModels(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchModelsFromDatabase]\"], [\n        configId,\n        getCachedData\n    ]);\n    // New: Fetch all models from DB when configId is available (i.e., page is ready)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            if (configId) {\n                fetchModelsFromDatabase();\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        configId,\n        fetchModelsFromDatabase\n    ]);\n    // Updated: Function to fetch all global custom roles for the authenticated user with caching\n    const fetchUserCustomRoles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\": async ()=>{\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.userCustomRoles) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached custom roles data for: \".concat(configId));\n                setUserCustomRoles(cachedData.userCustomRoles);\n                setIsLoadingUserCustomRoles(false);\n                return;\n            }\n            setIsLoadingUserCustomRoles(true);\n            setUserCustomRolesError(null);\n            try {\n                const response = await fetch(\"/api/user/custom-roles\"); // New global endpoint\n                if (!response.ok) {\n                    let errorData;\n                    try {\n                        errorData = await response.json(); // Attempt to parse error as JSON\n                    } catch (e) {\n                        // If error response is not JSON, use text or a generic error\n                        const errorText = await response.text().catch({\n                            \"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\": ()=>\"HTTP error \".concat(response.status)\n                        }[\"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\"]);\n                        errorData = {\n                            error: errorText\n                        };\n                    }\n                    const errorMessage = errorData.error || (errorData.issues ? JSON.stringify(errorData.issues) : \"Failed to fetch custom roles (status: \".concat(response.status, \")\"));\n                    if (response.status === 401) {\n                        setUserCustomRolesError(errorMessage);\n                    } else {\n                        throw new Error(errorMessage); // Throw for other errors to be caught by the main catch\n                    }\n                    setUserCustomRoles([]); // Clear roles if there was an error handled here\n                } else {\n                    // Only call .json() here if response.ok and body hasn't been read\n                    const data = await response.json();\n                    setUserCustomRoles(data);\n                // setUserCustomRolesError(null); // Clearing error on success is good, but already done at the start of try\n                }\n            } catch (err) {\n                // This catch handles network errors from fetch() or errors thrown from !response.ok block\n                setUserCustomRolesError(err.message);\n                setUserCustomRoles([]); // Clear roles on error\n            } finally{\n                setIsLoadingUserCustomRoles(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\"], []);\n    // Fetch API keys and their roles for this config with optimistic loading\n    const fetchKeysAndRolesForConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\": async ()=>{\n            if (!configId || !userCustomRoles) return; // Also wait for userCustomRoles to be available\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.apiKeys && cachedData.defaultChatKeyId !== undefined) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached keys data for: \".concat(configId));\n                // Process cached keys with roles (same logic as below)\n                const keysWithRolesPromises = cachedData.apiKeys.map({\n                    \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": async (key)=>{\n                        const rolesResponse = await fetch(\"/api/keys/\".concat(key.id, \"/roles\"));\n                        let assigned_roles = [];\n                        if (rolesResponse.ok) {\n                            const roleAssignments = await rolesResponse.json();\n                            assigned_roles = roleAssignments.map({\n                                \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": (ra)=>{\n                                    const predefinedRole = (0,_config_roles__WEBPACK_IMPORTED_MODULE_4__.getRoleById)(ra.role_name);\n                                    if (predefinedRole) return predefinedRole;\n                                    const customRole = userCustomRoles.find({\n                                        \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\": (cr)=>cr.role_id === ra.role_name\n                                    }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\"]);\n                                    if (customRole) {\n                                        return {\n                                            id: customRole.role_id,\n                                            name: customRole.name,\n                                            description: customRole.description || undefined\n                                        };\n                                    }\n                                    return null;\n                                }\n                            }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]).filter(Boolean);\n                        }\n                        return {\n                            ...key,\n                            assigned_roles,\n                            is_default_general_chat_model: cachedData.defaultChatKeyId === key.id\n                        };\n                    }\n                }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]);\n                const resolvedKeysWithRoles = await Promise.all(keysWithRolesPromises);\n                setSavedKeysWithRoles(resolvedKeysWithRoles);\n                setDefaultGeneralChatKeyId(cachedData.defaultChatKeyId);\n                setIsLoadingKeysAndRoles(false);\n                return;\n            }\n            setIsLoadingKeysAndRoles(true);\n            // Preserve config loading errors, clear others\n            setError({\n                \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\": (prev)=>prev && prev.startsWith('Error loading model configuration:') ? prev : null\n            }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\"]);\n            setSuccessMessage(null);\n            try {\n                // Fetch all keys for the config\n                const keysResponse = await fetch(\"/api/keys?custom_config_id=\".concat(configId));\n                if (!keysResponse.ok) {\n                    const errorData = await keysResponse.json();\n                    throw new Error(errorData.error || 'Failed to fetch API keys');\n                }\n                const keys = await keysResponse.json();\n                // Fetch default general chat key\n                const defaultKeyResponse = await fetch(\"/api/custom-configs/\".concat(configId, \"/default-chat-key\"));\n                if (!defaultKeyResponse.ok) {\n                    console.warn('Failed to fetch default chat key info');\n                }\n                const defaultKeyData = defaultKeyResponse.status === 200 ? await defaultKeyResponse.json() : null;\n                setDefaultGeneralChatKeyId((defaultKeyData === null || defaultKeyData === void 0 ? void 0 : defaultKeyData.id) || null);\n                // For each key, fetch its assigned roles\n                const keysWithRolesPromises = keys.map({\n                    \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": async (key)=>{\n                        const rolesResponse = await fetch(\"/api/keys/\".concat(key.id, \"/roles\"));\n                        let assigned_roles = [];\n                        if (rolesResponse.ok) {\n                            const roleAssignments = await rolesResponse.json();\n                            assigned_roles = roleAssignments.map({\n                                \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": (ra)=>{\n                                    // 1. Check predefined roles\n                                    const predefinedRole = (0,_config_roles__WEBPACK_IMPORTED_MODULE_4__.getRoleById)(ra.role_name);\n                                    if (predefinedRole) return predefinedRole;\n                                    // 2. Check current user's global custom roles\n                                    const customRole = userCustomRoles.find({\n                                        \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\": (cr)=>cr.role_id === ra.role_name\n                                    }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\"]);\n                                    if (customRole) {\n                                        return {\n                                            id: customRole.role_id,\n                                            name: customRole.name,\n                                            description: customRole.description || undefined\n                                        };\n                                    }\n                                    // 3. If not found in either, it's a lingering assignment to a deleted/invalid role, so filter it out\n                                    return null;\n                                }\n                            }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]).filter(Boolean); // filter(Boolean) removes null entries\n                        }\n                        return {\n                            ...key,\n                            assigned_roles,\n                            is_default_general_chat_model: (defaultKeyData === null || defaultKeyData === void 0 ? void 0 : defaultKeyData.id) === key.id\n                        };\n                    }\n                }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]);\n                const resolvedKeysWithRoles = await Promise.all(keysWithRolesPromises);\n                setSavedKeysWithRoles(resolvedKeysWithRoles);\n            } catch (err) {\n                setError({\n                    \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\": (prev)=>prev ? \"\".concat(prev, \"; \").concat(err.message) : err.message\n                }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\"]); // Append if there was a config load error\n            } finally{\n                setIsLoadingKeysAndRoles(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\"], [\n        configId,\n        userCustomRoles\n    ]); // Added userCustomRoles to dependency array\n    // Fetch browser automation status\n    const fetchBrowserAutomationStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchBrowserAutomationStatus]\": async ()=>{\n            if (!configId) return;\n            try {\n                const response = await fetch(\"/api/custom-configs/\".concat(configId, \"/browser-automation-status\"));\n                if (response.ok) {\n                    const data = await response.json();\n                    setBrowserAutomationEnabled(data.enabled || false);\n                    setUserTier(data.user_tier || 'free');\n                }\n            } catch (error) {\n                console.warn('Could not fetch browser automation status:', error);\n            // Don't show error to user as this is optional functionality\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchBrowserAutomationStatus]\"], [\n        configId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            if (configDetails) {\n                fetchUserCustomRoles(); // Call to fetch custom roles\n                fetchBrowserAutomationStatus(); // Fetch browser automation status\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        configDetails,\n        fetchUserCustomRoles,\n        fetchBrowserAutomationStatus\n    ]); // Only depends on configDetails and the stable fetchUserCustomRoles\n    // New useEffect to fetch keys and roles when configDetails AND userCustomRoles (state) are ready\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            // Ensure userCustomRoles is not in its initial undefined/null state from useState([])\n            // and actually contains data (or an empty array confirming fetch completion)\n            if (configDetails && userCustomRoles) {\n                fetchKeysAndRolesForConfig();\n            }\n        // This effect runs if configDetails changes, userCustomRoles (state) changes,\n        // or fetchKeysAndRolesForConfig function identity changes (which happens if userCustomRoles state changes).\n        // This is the desired behavior: re-fetch keys/roles if custom roles change.\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        configDetails,\n        userCustomRoles,\n        fetchKeysAndRolesForConfig\n    ]);\n    // Updated: Memoize model options based on selected provider and fetched models\n    const modelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ConfigDetailsPage.useMemo[modelOptions]\": ()=>{\n            if (fetchedProviderModels) {\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.find({\n                    \"ConfigDetailsPage.useMemo[modelOptions].currentProviderDetails\": (p)=>p.id === provider\n                }[\"ConfigDetailsPage.useMemo[modelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) return [];\n                // If the selected provider is \"OpenRouter\", show all fetched models\n                // as an OpenRouter key can access any of them.\n                if (currentProviderDetails.id === \"openrouter\") {\n                    return fetchedProviderModels.map({\n                        \"ConfigDetailsPage.useMemo[modelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"ConfigDetailsPage.useMemo[modelOptions]\"]).sort({\n                        \"ConfigDetailsPage.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[modelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    console.log('[DeepSeek Debug] Provider is DeepSeek. Fetched models:', JSON.stringify(fetchedProviderModels));\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[modelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[modelOptions].deepseekChatModel\"]);\n                    console.log('[DeepSeek Debug] Found deepseek-chat model:', JSON.stringify(deepseekChatModel));\n                    if (deepseekChatModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[modelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[modelOptions].deepseekReasonerModel\"]);\n                    console.log('[DeepSeek Debug] Found deepseek-reasoner model:', JSON.stringify(deepseekReasonerModel));\n                    if (deepseekReasonerModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    // If for some reason the specific models are not found in fetchedProviderModels,\n                    // it's better to return an empty array or a message than all DeepSeek models unfiltered.\n                    // Or, as a fallback, show all models for DeepSeek if the specific ones aren't present.\n                    // For now, strictly showing only these two if found.\n                    return deepseekOptions.sort({\n                        \"ConfigDetailsPage.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[modelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id\n                return fetchedProviderModels.filter({\n                    \"ConfigDetailsPage.useMemo[modelOptions]\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"ConfigDetailsPage.useMemo[modelOptions]\"]).map({\n                    \"ConfigDetailsPage.useMemo[modelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"ConfigDetailsPage.useMemo[modelOptions]\"]).sort({\n                    \"ConfigDetailsPage.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"ConfigDetailsPage.useMemo[modelOptions]\"]);\n            }\n            return []; // Return empty array if models haven't been fetched or if fetch failed.\n        }\n    }[\"ConfigDetailsPage.useMemo[modelOptions]\"], [\n        fetchedProviderModels,\n        provider\n    ]);\n    // Model options for edit modal - filtered by the current key's provider\n    const editModelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ConfigDetailsPage.useMemo[editModelOptions]\": ()=>{\n            if (fetchedProviderModels && editingApiKey) {\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.find({\n                    \"ConfigDetailsPage.useMemo[editModelOptions].currentProviderDetails\": (p)=>p.id === editingApiKey.provider\n                }[\"ConfigDetailsPage.useMemo[editModelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) return [];\n                // If the provider is \"OpenRouter\", show all fetched models\n                if (currentProviderDetails.id === \"openrouter\") {\n                    return fetchedProviderModels.map({\n                        \"ConfigDetailsPage.useMemo[editModelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]).sort({\n                        \"ConfigDetailsPage.useMemo[editModelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[editModelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions].deepseekChatModel\"]);\n                    if (deepseekChatModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[editModelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions].deepseekReasonerModel\"]);\n                    if (deepseekReasonerModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    return deepseekOptions.sort({\n                        \"ConfigDetailsPage.useMemo[editModelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id\n                return fetchedProviderModels.filter({\n                    \"ConfigDetailsPage.useMemo[editModelOptions]\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]).map({\n                    \"ConfigDetailsPage.useMemo[editModelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]).sort({\n                    \"ConfigDetailsPage.useMemo[editModelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]);\n            }\n            return [];\n        }\n    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"], [\n        fetchedProviderModels,\n        editingApiKey\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            // Auto-select the first model from the dynamic modelOptions when provider changes or models load\n            if (modelOptions.length > 0) {\n                setPredefinedModelId(modelOptions[0].value);\n            } else {\n                setPredefinedModelId(''); // Clear if no models for provider\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        modelOptions,\n        provider\n    ]); // Now depends on modelOptions, which depends on fetchedProviderModels and provider\n    // Fetch models based on the provider's slug (p.id)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            if (provider) {\n                // Logic to fetch models for the selected provider slug might need adjustment\n                // if it was previously relying on the provider display name.\n                // Assuming fetchProviderModels is adapted or already uses slugs.\n                fetchModelsFromDatabase();\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        provider,\n        fetchModelsFromDatabase\n    ]);\n    const handleSaveKey = async (e)=>{\n        e.preventDefault();\n        if (!configId) {\n            setError('Configuration ID is missing.');\n            return;\n        }\n        // Frontend validation: Check for duplicate models\n        const isDuplicateModel = savedKeysWithRoles.some((key)=>key.predefined_model_id === predefinedModelId);\n        if (isDuplicateModel) {\n            setError('This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.');\n            return;\n        }\n        setIsSavingKey(true);\n        setError(null);\n        setSuccessMessage(null);\n        // provider state variable already holds the slug\n        const newKeyData = {\n            custom_api_config_id: configId,\n            provider,\n            predefined_model_id: predefinedModelId,\n            api_key_raw: apiKeyRaw,\n            label,\n            temperature\n        };\n        // Store previous state for rollback on error\n        const previousKeysState = [\n            ...savedKeysWithRoles\n        ];\n        try {\n            var _PROVIDER_OPTIONS_;\n            const response = await fetch('/api/keys', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(newKeyData)\n            });\n            const result = await response.json();\n            if (!response.ok) throw new Error(result.details || result.error || 'Failed to save API key');\n            // Create optimistic key object with the returned data\n            const newKey = {\n                id: result.id,\n                custom_api_config_id: configId,\n                provider,\n                predefined_model_id: predefinedModelId,\n                label,\n                temperature,\n                status: 'active',\n                created_at: new Date().toISOString(),\n                last_used_at: null,\n                is_default_general_chat_model: false,\n                assigned_roles: []\n            };\n            // Optimistically add the new key to the list\n            setSavedKeysWithRoles((prevKeys)=>[\n                    ...prevKeys,\n                    newKey\n                ]);\n            setSuccessMessage('API key \"'.concat(label, '\" saved successfully!'));\n            setProvider(((_PROVIDER_OPTIONS_ = PROVIDER_OPTIONS[0]) === null || _PROVIDER_OPTIONS_ === void 0 ? void 0 : _PROVIDER_OPTIONS_.value) || 'openai');\n            setApiKeyRaw('');\n            setLabel('');\n            setTemperature(1.0);\n            // Reset model selection to first available option\n            if (modelOptions.length > 0) {\n                setPredefinedModelId(modelOptions[0].value);\n            }\n        } catch (err) {\n            // Revert UI on error\n            setSavedKeysWithRoles(previousKeysState);\n            setError(\"Save Key Error: \".concat(err.message));\n        } finally{\n            setIsSavingKey(false);\n        }\n    };\n    const handleEditKey = (key)=>{\n        setEditingApiKey(key);\n        setEditTemperature(key.temperature || 1.0);\n        setEditPredefinedModelId(key.predefined_model_id);\n    };\n    const handleSaveEdit = async ()=>{\n        if (!editingApiKey) return;\n        // Frontend validation: Check for duplicate models (excluding the current key being edited)\n        const isDuplicateModel = savedKeysWithRoles.some((key)=>key.id !== editingApiKey.id && key.predefined_model_id === editPredefinedModelId);\n        if (isDuplicateModel) {\n            setError('This model is already configured in this setup. Each model can only be used once per configuration.');\n            return;\n        }\n        setIsSavingEdit(true);\n        setError(null);\n        setSuccessMessage(null);\n        // Store previous state for rollback on error\n        const previousKeysState = [\n            ...savedKeysWithRoles\n        ];\n        // Optimistic UI update\n        setSavedKeysWithRoles((prevKeys)=>prevKeys.map((key)=>{\n                if (key.id === editingApiKey.id) {\n                    return {\n                        ...key,\n                        temperature: editTemperature,\n                        predefined_model_id: editPredefinedModelId\n                    };\n                }\n                return key;\n            }));\n        try {\n            const response = await fetch(\"/api/keys?id=\".concat(editingApiKey.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    temperature: editTemperature,\n                    predefined_model_id: editPredefinedModelId\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                // Revert UI on error\n                setSavedKeysWithRoles(previousKeysState);\n                throw new Error(result.details || result.error || 'Failed to update API key');\n            }\n            setSuccessMessage('API key \"'.concat(editingApiKey.label, '\" updated successfully!'));\n            setEditingApiKey(null);\n        } catch (err) {\n            setError(\"Update Key Error: \".concat(err.message));\n        } finally{\n            setIsSavingEdit(false);\n        }\n    };\n    const handleDeleteKey = (keyId, keyLabel)=>{\n        confirmation.showConfirmation({\n            title: 'Delete API Key',\n            message: 'Are you sure you want to delete the API key \"'.concat(keyLabel, '\"? This will permanently remove the key and unassign all its roles. This action cannot be undone.'),\n            confirmText: 'Delete API Key',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            setIsDeletingKey(keyId);\n            setError(null);\n            setSuccessMessage(null);\n            // Store previous state for rollback on error\n            const previousKeysState = [\n                ...savedKeysWithRoles\n            ];\n            const previousDefaultKeyId = defaultGeneralChatKeyId;\n            const keyToDelete = savedKeysWithRoles.find((key)=>key.id === keyId);\n            // Optimistic UI update - immediately remove the key from the list\n            setSavedKeysWithRoles((prevKeys)=>prevKeys.filter((key)=>key.id !== keyId));\n            // If the deleted key was the default, clear the default\n            if (keyToDelete === null || keyToDelete === void 0 ? void 0 : keyToDelete.is_default_general_chat_model) {\n                setDefaultGeneralChatKeyId(null);\n            }\n            try {\n                const response = await fetch(\"/api/keys/\".concat(keyId), {\n                    method: 'DELETE'\n                });\n                const result = await response.json();\n                if (!response.ok) {\n                    // Revert UI on error\n                    setSavedKeysWithRoles(previousKeysState);\n                    setDefaultGeneralChatKeyId(previousDefaultKeyId);\n                    // Special handling for 404 errors (key already deleted)\n                    if (response.status === 404) {\n                        // Key was already deleted, so the optimistic update was correct\n                        // Don't revert the UI, just show a different message\n                        setSavedKeysWithRoles((prevKeys)=>prevKeys.filter((key)=>key.id !== keyId));\n                        setSuccessMessage('API key \"'.concat(keyLabel, '\" was already deleted.'));\n                        return; // Don't throw error\n                    }\n                    throw new Error(result.details || result.error || 'Failed to delete API key');\n                }\n                setSuccessMessage('API key \"'.concat(keyLabel, '\" deleted successfully!'));\n            } catch (err) {\n                setError(\"Delete Key Error: \".concat(err.message));\n                throw err; // Re-throw to keep modal open on error\n            } finally{\n                setIsDeletingKey(null);\n            }\n        });\n    };\n    const handleSetDefaultChatKey = async (apiKeyIdToSet)=>{\n        if (!configId) return;\n        setError(null);\n        setSuccessMessage(null);\n        const previousKeysState = [\n            ...savedKeysWithRoles\n        ]; // Keep a copy in case of error\n        const previousDefaultKeyId = defaultGeneralChatKeyId;\n        // Optimistic UI update\n        setSavedKeysWithRoles((prevKeys)=>prevKeys.map((key)=>({\n                    ...key,\n                    is_default_general_chat_model: key.id === apiKeyIdToSet\n                })));\n        setDefaultGeneralChatKeyId(apiKeyIdToSet); // Update the separate state for default ID\n        try {\n            const response = await fetch(\"/api/custom-configs/\".concat(configId, \"/default-key-handler/\").concat(apiKeyIdToSet), {\n                method: 'PUT'\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                // Revert UI on error\n                setSavedKeysWithRoles(previousKeysState.map((k)=>({\n                        ...k\n                    }))); // Ensure deep copy for re-render\n                setDefaultGeneralChatKeyId(previousDefaultKeyId);\n                throw new Error(result.details || result.error || 'Failed to set default chat key');\n            }\n            setSuccessMessage(result.message || 'Default general chat key updated!');\n        } catch (err) {\n            setError(\"Set Default Error: \".concat(err.message));\n        }\n    };\n    const handleRoleToggle = async (apiKey, roleId, isAssigned)=>{\n        setError(null);\n        setSuccessMessage(null);\n        const endpoint = \"/api/keys/\".concat(apiKey.id, \"/roles\");\n        // For optimistic update, find role details from combined list (predefined or user's global custom roles)\n        const allAvailableRoles = [\n            ..._config_roles__WEBPACK_IMPORTED_MODULE_4__.PREDEFINED_ROLES.map((r)=>({\n                    ...r,\n                    isCustom: false\n                })),\n            ...userCustomRoles.map((cr)=>({\n                    id: cr.role_id,\n                    name: cr.name,\n                    description: cr.description || undefined,\n                    isCustom: true,\n                    databaseId: cr.id\n                }))\n        ];\n        const roleDetails = allAvailableRoles.find((r)=>r.id === roleId) || {\n            id: roleId,\n            name: roleId,\n            description: ''\n        };\n        const previousKeysState = savedKeysWithRoles.map((k)=>({\n                ...k,\n                assigned_roles: [\n                    ...k.assigned_roles.map((r)=>({\n                            ...r\n                        }))\n                ]\n            })); // Deep copy\n        let previousEditingRolesApiKey = null;\n        if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {\n            previousEditingRolesApiKey = {\n                ...editingRolesApiKey,\n                assigned_roles: [\n                    ...editingRolesApiKey.assigned_roles.map((r)=>({\n                            ...r\n                        }))\n                ]\n            };\n        }\n        // Optimistic UI update\n        setSavedKeysWithRoles((prevKeys)=>prevKeys.map((key)=>{\n                if (key.id === apiKey.id) {\n                    const updatedRoles = isAssigned ? key.assigned_roles.filter((r)=>r.id !== roleId) : [\n                        ...key.assigned_roles,\n                        roleDetails\n                    ];\n                    return {\n                        ...key,\n                        assigned_roles: updatedRoles\n                    };\n                }\n                return key;\n            }));\n        if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {\n            setEditingRolesApiKey((prevEditingKey)=>{\n                if (!prevEditingKey) return null;\n                const updatedRoles = isAssigned ? prevEditingKey.assigned_roles.filter((r)=>r.id !== roleId) : [\n                    ...prevEditingKey.assigned_roles,\n                    roleDetails\n                ];\n                return {\n                    ...prevEditingKey,\n                    assigned_roles: updatedRoles\n                };\n            });\n        }\n        try {\n            let response;\n            if (isAssigned) {\n                response = await fetch(\"\".concat(endpoint, \"/\").concat(roleId), {\n                    method: 'DELETE'\n                });\n            } else {\n                response = await fetch(endpoint, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        role_name: roleId\n                    })\n                });\n            }\n            const result = await response.json();\n            if (!response.ok) {\n                // Revert UI on error\n                setSavedKeysWithRoles(previousKeysState);\n                if (previousEditingRolesApiKey) {\n                    setEditingRolesApiKey(previousEditingRolesApiKey);\n                } else if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {\n                    const originalKeyData = previousKeysState.find((k)=>k.id === apiKey.id);\n                    if (originalKeyData) setEditingRolesApiKey(originalKeyData);\n                }\n                // Use the error message from the backend if available (e.g., for 409 conflict)\n                const errorMessage = response.status === 409 && result.error ? result.error : result.details || result.error || (isAssigned ? 'Failed to unassign role' : 'Failed to assign role');\n                throw new Error(errorMessage);\n            }\n            setSuccessMessage(result.message || \"Role '\".concat(roleDetails.name, \"' \").concat(isAssigned ? 'unassigned' : 'assigned', \" successfully.\"));\n        } catch (err) {\n            // err.message now contains the potentially more user-friendly message from the backend or a fallback\n            setError(\"Role Update Error: \".concat(err.message));\n        }\n    };\n    const handleCreateCustomRole = async ()=>{\n        // Removed editingRolesApiKey check as creating a global role isn't tied to a specific key being edited.\n        // configId is also not needed for creating a global role.\n        if (!newCustomRoleId.trim() || newCustomRoleId.trim().length > 30 || !/^[a-zA-Z0-9_]+$/.test(newCustomRoleId.trim())) {\n            setCreateCustomRoleError('Role ID is required (max 30 chars, letters, numbers, underscores only).');\n            return;\n        }\n        // Check against PREDEFINED_ROLES and the user's existing global custom roles\n        if (_config_roles__WEBPACK_IMPORTED_MODULE_4__.PREDEFINED_ROLES.some((pr)=>pr.id.toLowerCase() === newCustomRoleId.trim().toLowerCase()) || userCustomRoles.some((cr)=>cr.role_id.toLowerCase() === newCustomRoleId.trim().toLowerCase())) {\n            setCreateCustomRoleError('This Role ID is already in use (either predefined or as one of your custom roles).');\n            return;\n        }\n        if (!newCustomRoleName.trim()) {\n            setCreateCustomRoleError('Role Name is required.');\n            return;\n        }\n        setCreateCustomRoleError(null);\n        setIsSavingCustomRole(true);\n        try {\n            const response = await fetch(\"/api/user/custom-roles\", {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    role_id: newCustomRoleId.trim(),\n                    name: newCustomRoleName.trim(),\n                    description: newCustomRoleDescription.trim()\n                })\n            });\n            if (!response.ok) {\n                // Try to parse the error response as JSON, but fallback if it's not JSON\n                let errorResult;\n                try {\n                    errorResult = await response.json();\n                } catch (parseError) {\n                    // If JSON parsing fails, use the response text or a generic status message\n                    const errorText = await response.text().catch(()=>\"HTTP status \".concat(response.status));\n                    errorResult = {\n                        error: \"Server error, could not parse response.\",\n                        details: errorText\n                    };\n                }\n                let displayError = errorResult.error || 'Failed to create custom role.';\n                if (errorResult.details) {\n                    displayError += \" (Details: \".concat(errorResult.details, \")\");\n                } else if (errorResult.issues) {\n                    // If Zod issues, format them for better readability\n                    const issuesString = Object.entries(errorResult.issues).map((param)=>{\n                        let [field, messages] = param;\n                        return \"\".concat(field, \": \").concat(messages.join(', '));\n                    }).join('; ');\n                    displayError += \" (Issues: \".concat(issuesString, \")\");\n                }\n                throw new Error(displayError);\n            }\n            // If response IS ok, then parse the successful JSON response\n            const result = await response.json();\n            setNewCustomRoleId('');\n            setNewCustomRoleName('');\n            setNewCustomRoleDescription('');\n            // setShowCreateCustomRoleForm(false); // User might want to add multiple roles\n            fetchUserCustomRoles(); // Refresh the global list\n            setSuccessMessage(\"Custom role '\".concat(result.name, \"' created successfully! It is now available globally.\"));\n        } catch (err) {\n            setCreateCustomRoleError(err.message);\n        } finally{\n            setIsSavingCustomRole(false);\n        }\n    };\n    const handleDeleteCustomRole = (customRoleDatabaseId, customRoleName)=>{\n        // configId is not needed for deleting a global role\n        if (!customRoleDatabaseId) return;\n        confirmation.showConfirmation({\n            title: 'Delete Custom Role',\n            message: 'Are you sure you want to delete the custom role \"'.concat(customRoleName, \"\\\"? This will unassign it from all API keys where it's currently used. This action cannot be undone.\"),\n            confirmText: 'Delete Role',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            setDeletingCustomRoleId(customRoleDatabaseId);\n            setUserCustomRolesError(null);\n            setCreateCustomRoleError(null);\n            setSuccessMessage(null);\n            try {\n                const response = await fetch(\"/api/user/custom-roles/\".concat(customRoleDatabaseId), {\n                    method: 'DELETE'\n                });\n                const result = await response.json(); // Try to parse JSON for all responses\n                if (!response.ok) {\n                    throw new Error(result.error || 'Failed to delete custom role');\n                }\n                // Optimistically remove from the local state\n                setUserCustomRoles((prev)=>prev.filter((role)=>role.id !== customRoleDatabaseId));\n                setSuccessMessage(result.message || 'Global custom role \"'.concat(customRoleName, '\" deleted successfully.'));\n                // Re-fetch keys and roles for the current config, as the deleted global role might have been assigned here.\n                // This ensures the displayed assigned roles for keys on this page are up-to-date.\n                if (configId) {\n                    fetchKeysAndRolesForConfig();\n                }\n            } catch (err) {\n                setUserCustomRolesError(\"Error deleting role: \".concat(err.message));\n                throw err; // Re-throw to keep modal open on error\n            } finally{\n                setDeletingCustomRoleId(null);\n            }\n        });\n    };\n    // Browser automation toggle handler\n    const handleBrowserAutomationToggle = async (enabled)=>{\n        if (!configId) return;\n        setBrowserAutomationLoading(true);\n        setBrowserAutomationError(null);\n        try {\n            const response = await fetch(\"/api/custom-configs/\".concat(configId, \"/browser-automation\"), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    enabled\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Failed to update browser automation setting');\n            }\n            setBrowserAutomationEnabled(enabled);\n            setSuccessMessage(\"Browser automation \".concat(enabled ? 'enabled' : 'disabled', \" successfully!\"));\n        } catch (error) {\n            setBrowserAutomationError(error.message);\n            setError(\"Browser Automation Error: \".concat(error.message));\n        } finally{\n            setBrowserAutomationLoading(false);\n        }\n    };\n    const renderManageRolesModal = ()=>{\n        if (!editingRolesApiKey) return null;\n        const combinedRoles = [\n            ..._config_roles__WEBPACK_IMPORTED_MODULE_4__.PREDEFINED_ROLES.map((r)=>({\n                    ...r,\n                    isCustom: false\n                })),\n            ...userCustomRoles.map((cr)=>({\n                    id: cr.role_id,\n                    name: cr.name,\n                    description: cr.description || undefined,\n                    isCustom: true,\n                    databaseId: cr.id // The actual DB ID (UUID) for delete operations\n                }))\n        ].sort((a, b)=>a.name.localeCompare(b.name));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card w-full max-w-lg max-h-[90vh] flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center p-6 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900\",\n                                children: [\n                                    \"Manage Roles for: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-orange-600\",\n                                        children: editingRolesApiKey.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 994,\n                                        columnNumber: 83\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 994,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setEditingRolesApiKey(null);\n                                    setShowCreateCustomRoleForm(false);\n                                    setCreateCustomRoleError(null);\n                                },\n                                className: \"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 996,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 995,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 993,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-gray-200\",\n                        children: [\n                            userCustomRolesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-800 text-sm\",\n                                    children: [\n                                        \"Error with custom roles: \",\n                                        userCustomRolesError\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1003,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1002,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCreateCustomRoleForm(!showCreateCustomRoleForm),\n                                    className: \"btn-primary text-sm inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1012,\n                                            columnNumber: 17\n                                        }, this),\n                                        showCreateCustomRoleForm ? 'Cancel New Role' : 'Create New Custom Role'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1008,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1007,\n                                columnNumber: 13\n                            }, this),\n                            showCreateCustomRoleForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-md font-medium text-gray-900 mb-3\",\n                                        children: \"Create New Custom Role for this Configuration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1019,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"newCustomRoleId\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Role ID (short, no spaces, max 30 chars)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1022,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"newCustomRoleId\",\n                                                        value: newCustomRoleId,\n                                                        onChange: (e)=>setNewCustomRoleId(e.target.value.replace(/\\s/g, '')),\n                                                        className: \"form-input\",\n                                                        maxLength: 30,\n                                                        placeholder: \"e.g., my_blog_writer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1023,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1021,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"newCustomRoleName\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Display Name (max 100 chars)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1032,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"newCustomRoleName\",\n                                                        value: newCustomRoleName,\n                                                        onChange: (e)=>setNewCustomRoleName(e.target.value),\n                                                        className: \"form-input\",\n                                                        maxLength: 100,\n                                                        placeholder: \"e.g., My Awesome Blog Writer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1033,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1031,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"newCustomRoleDescription\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Description (optional, max 500 chars)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1042,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        id: \"newCustomRoleDescription\",\n                                                        value: newCustomRoleDescription,\n                                                        onChange: (e)=>setNewCustomRoleDescription(e.target.value),\n                                                        rows: 2,\n                                                        className: \"form-input\",\n                                                        maxLength: 500,\n                                                        placeholder: \"Optional: Describe what this role is for...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1043,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1041,\n                                                columnNumber: 19\n                                            }, this),\n                                            createCustomRoleError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-800 text-sm\",\n                                                    children: createCustomRoleError\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1054,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1053,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleCreateCustomRole,\n                                                disabled: isSavingCustomRole,\n                                                className: \"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: isSavingCustomRole ? 'Saving Role...' : 'Save Custom Role'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1057,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1020,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1018,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1000,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-700 mb-3\",\n                                children: \"Select roles to assign:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1070,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-y-auto space-y-2\",\n                                style: {\n                                    maxHeight: 'calc(90vh - 350px)'\n                                },\n                                children: [\n                                    isLoadingUserCustomRoles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1074,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm ml-2\",\n                                                children: \"Loading custom roles...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1075,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1073,\n                                        columnNumber: 17\n                                    }, this),\n                                    combinedRoles.map((role)=>{\n                                        const isAssigned = editingRolesApiKey.assigned_roles.some((ar)=>ar.id === role.id);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 rounded-lg border transition-all duration-200 \".concat(isAssigned ? 'bg-orange-50 border-orange-200 shadow-sm' : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"role-\".concat(role.id),\n                                                    className: \"flex items-center cursor-pointer flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            id: \"role-\".concat(role.id),\n                                                            checked: isAssigned,\n                                                            onChange: ()=>handleRoleToggle(editingRolesApiKey, role.id, isAssigned),\n                                                            className: \"h-4 w-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1087,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-3 text-sm font-medium \".concat(isAssigned ? 'text-orange-800' : 'text-gray-900'),\n                                                            children: role.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1094,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        role.isCustom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800\",\n                                                            children: \"Custom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1098,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1086,\n                                                    columnNumber: 21\n                                                }, this),\n                                                role.isCustom && role.databaseId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteCustomRole(role.databaseId, role.name),\n                                                    disabled: deletingCustomRoleId === role.databaseId,\n                                                    className: \"p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2\",\n                                                    title: \"Delete this custom role\",\n                                                    children: deletingCustomRoleId === role.databaseId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1110,\n                                                        columnNumber: 70\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1110,\n                                                        columnNumber: 123\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1104,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, role.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1081,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1071,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1069,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setEditingRolesApiKey(null);\n                                    setShowCreateCustomRoleForm(false);\n                                    setCreateCustomRoleError(null);\n                                },\n                                className: \"btn-secondary\",\n                                children: \"Done\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1121,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1120,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1119,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                lineNumber: 992,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 991,\n            columnNumber: 7\n        }, this);\n    };\n    // Main render logic with optimistic loading\n    if (showOptimisticLoading && !isCached(configId)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ManageKeysLoadingSkeleton__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1136,\n            columnNumber: 12\n        }, this);\n    }\n    if (isLoadingConfig && !configDetails) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ManageKeysLoadingSkeleton__WEBPACK_IMPORTED_MODULE_9__.CompactManageKeysLoadingSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1140,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>navigateOptimistically('/my-models'),\n                        className: \"text-orange-600 hover:text-orange-700 inline-flex items-center mb-6 transition-colors duration-200 group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1151,\n                                columnNumber: 11\n                            }, this),\n                            \"Back to My API Models\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: configDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-6 w-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1163,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1162,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"text-3xl font-bold text-gray-900\",\n                                                                children: configDetails.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1166,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 mt-1\",\n                                                                children: \"Model Configuration\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1169,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1165,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1161,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-sm text-gray-600 bg-gray-50 px-4 py-2 rounded-xl w-fit\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-block w-2 h-2 bg-orange-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1173,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"ID: \",\n                                                    configDetails.id\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1172,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : error && !isLoadingConfig ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-red-100 rounded-2xl flex items-center justify-center mr-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-6 w-6 text-red-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1180,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1179,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: \"Configuration Error\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1183,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 mt-1\",\n                                                        children: error.replace(\"Error loading model configuration: \", \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1184,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1182,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1178,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gray-100 rounded-2xl flex items-center justify-center mr-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-6 w-6 text-gray-400 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1190,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1189,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: \"Loading Configuration...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1193,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 mt-1\",\n                                                        children: \"Please wait while we fetch your model details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1194,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1192,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1188,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1158,\n                                    columnNumber: 13\n                                }, this),\n                                configDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-2xl border border-gray-200 p-4 shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1208,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1207,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: \"Browser Automation\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1211,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: userTier === 'free' ? 'Upgrade to enable' : userTier === 'starter' ? '15 tasks/month' : 'Unlimited tasks'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1212,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1210,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1206,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                browserAutomationLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1221,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: browserAutomationEnabled,\n                                                                            onChange: (e)=>handleBrowserAutomationToggle(e.target.checked),\n                                                                            disabled: browserAutomationLoading || userTier === 'free',\n                                                                            className: \"sr-only peer\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1224,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 peer-disabled:opacity-50 peer-disabled:cursor-not-allowed\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1231,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1223,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1219,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1205,\n                                                    columnNumber: 19\n                                                }, this),\n                                                browserAutomationError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded-lg\",\n                                                    children: browserAutomationError\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1236,\n                                                    columnNumber: 21\n                                                }, this),\n                                                userTier === 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 text-xs text-amber-600 bg-amber-50 p-2 rounded-lg\",\n                                                    children: \"Browser automation requires Starter plan or higher\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1241,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1204,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>navigateOptimistically(\"/routing-setup/\".concat(configId, \"?from=model-config\")),\n                                            className: \"inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group\",\n                                            ...createRoutingHoverPrefetch(configId),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1253,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Advanced Routing Setup\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1248,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1202,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1157,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1156,\n                        columnNumber: 9\n                    }, this),\n                    successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 border border-green-200 rounded-2xl p-4 mb-6 animate-slide-in\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-5 w-5 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1265,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-800 font-medium\",\n                                    children: successMessage\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1266,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1264,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1263,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-2xl p-4 mb-6 animate-slide-in\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-5 w-5 text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1273,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-800 font-medium\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1274,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1272,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1271,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                lineNumber: 1146,\n                columnNumber: 7\n            }, this),\n            configDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 xl:grid-cols-5 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"xl:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl shadow-sm border border-gray-100 p-6 sticky top-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1287,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1286,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-bold text-gray-900\",\n                                                    children: \"Add API Key\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1290,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Configure new key\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1291,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1289,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1285,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSaveKey,\n                                    className: \"space-y-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"provider\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Provider\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1298,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"provider\",\n                                                            value: provider,\n                                                            onChange: (e)=>{\n                                                                setProvider(e.target.value);\n                                                            },\n                                                            className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm\",\n                                                            children: PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: option.value,\n                                                                    children: option.label\n                                                                }, option.value, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1308,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1301,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1297,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"apiKeyRaw\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"API Key\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1314,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"apiKeyRaw\",\n                                                            type: \"password\",\n                                                            value: apiKeyRaw,\n                                                            onChange: (e)=>setApiKeyRaw(e.target.value),\n                                                            className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm\",\n                                                            placeholder: \"Enter your API key\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1317,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-2 text-xs text-orange-600 flex items-center bg-orange-50 p-2 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-1 animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1328,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Fetching models...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1327,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded-lg\",\n                                                            children: fetchProviderModelsError\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1333,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1313,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"predefinedModelId\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Model Variant\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1338,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"predefinedModelId\",\n                                                            value: predefinedModelId,\n                                                            onChange: (e)=>setPredefinedModelId(e.target.value),\n                                                            disabled: !modelOptions.length,\n                                                            className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm disabled:bg-gray-50 disabled:text-gray-500\",\n                                                            children: modelOptions.length > 0 ? modelOptions.map((m)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: m.value,\n                                                                    children: m.label\n                                                                }, m.value, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1350,\n                                                                    columnNumber: 27\n                                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                disabled: true,\n                                                                children: fetchedProviderModels === null && isFetchingProviderModels ? \"Loading models...\" : \"Select a provider first\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1353,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1341,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1337,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"label\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Label\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1359,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"label\",\n                                                            value: label,\n                                                            onChange: (e)=>setLabel(e.target.value),\n                                                            required: true,\n                                                            className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm\",\n                                                            placeholder: \"e.g., My OpenAI GPT-4o Key #1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1362,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1358,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"temperature\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: [\n                                                                \"Temperature\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500 ml-1\",\n                                                                    children: \"(0.0 - 2.0)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1376,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1374,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"range\",\n                                                                    id: \"temperature\",\n                                                                    min: \"0\",\n                                                                    max: \"2\",\n                                                                    step: \"0.1\",\n                                                                    value: temperature,\n                                                                    onChange: (e)=>setTemperature(parseFloat(e.target.value)),\n                                                                    className: \"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1379,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: \"Conservative\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1390,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                min: \"0\",\n                                                                                max: \"2\",\n                                                                                step: \"0.1\",\n                                                                                value: temperature,\n                                                                                onChange: (e)=>setTemperature(Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 0))),\n                                                                                className: \"w-16 px-2 py-1 text-xs border border-gray-200 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1392,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1391,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: \"Creative\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1402,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1389,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: \"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1404,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1378,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1373,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1296,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isSavingKey || !predefinedModelId || predefinedModelId === '' || !apiKeyRaw.trim() || !label.trim(),\n                                            className: \"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm\",\n                                            children: isSavingKey ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1418,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Saving...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1417,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1423,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Add API Key\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1422,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1411,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1295,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1433,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-blue-900 mb-1\",\n                                                        children: \"Key Configuration Rules\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1435,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-blue-800 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"✅ \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Same API key, different models:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1437,\n                                                                        columnNumber: 28\n                                                                    }, this),\n                                                                    \" Allowed\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1437,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"✅ \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Different API keys, same model:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1438,\n                                                                        columnNumber: 28\n                                                                    }, this),\n                                                                    \" Allowed\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1438,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"❌ \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Same model twice:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1439,\n                                                                        columnNumber: 28\n                                                                    }, this),\n                                                                    \" Not allowed in one configuration\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1439,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1436,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1434,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1432,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1431,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1284,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1283,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"xl:col-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl shadow-sm border border-gray-100 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1452,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1451,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-bold text-gray-900\",\n                                                    children: \"API Keys & Roles\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1455,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Manage existing keys\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1456,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1454,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1450,\n                                    columnNumber: 15\n                                }, this),\n                                isLoadingKeysAndRoles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1462,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: \"Loading API keys...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1463,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1461,\n                                    columnNumber: 17\n                                }, this),\n                                !isLoadingKeysAndRoles && savedKeysWithRoles.length === 0 && (!error || error && error.startsWith(\"Error loading model configuration:\")) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-6 w-6 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1470,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1469,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-semibold text-gray-900 mb-1\",\n                                            children: \"No API Keys\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1472,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"Add your first key using the form\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1473,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1468,\n                                    columnNumber: 17\n                                }, this),\n                                !isLoadingKeysAndRoles && savedKeysWithRoles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                    children: savedKeysWithRoles.map((key, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200 animate-slide-in\",\n                                            style: {\n                                                animationDelay: \"\".concat(index * 50, \"ms\")\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900 truncate mr-2\",\n                                                                        children: key.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1484,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    key.is_default_general_chat_model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 flex-shrink-0\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1487,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            \"Default\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1486,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1483,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-900 bg-white px-2 py-1 rounded-lg border\",\n                                                                                children: [\n                                                                                    key.provider,\n                                                                                    \" (\",\n                                                                                    key.predefined_model_id,\n                                                                                    \")\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1495,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded-lg border border-orange-200\",\n                                                                                children: [\n                                                                                    \"Temp: \",\n                                                                                    key.temperature\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1498,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1494,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-wrap gap-1\",\n                                                                        children: key.assigned_roles.length > 0 ? key.assigned_roles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"inline-block whitespace-nowrap rounded-full bg-orange-100 px-2 py-1 text-xs font-medium text-orange-800\",\n                                                                                children: role.name\n                                                                            }, role.id, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1506,\n                                                                                columnNumber: 35\n                                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500 bg-white px-2 py-1 rounded-lg border\",\n                                                                            children: \"No roles\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1511,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1503,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1493,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            !key.is_default_general_chat_model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSetDefaultChatKey(key.id),\n                                                                className: \"text-xs bg-white border border-gray-200 hover:border-gray-300 text-gray-700 hover:text-gray-900 py-1 px-2 rounded-lg mt-2 transition-colors\",\n                                                                \"data-tooltip-id\": \"global-tooltip\",\n                                                                \"data-tooltip-content\": \"Set as default chat model\",\n                                                                children: \"Set Default\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1517,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1482,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 ml-2 flex-shrink-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleEditKey(key),\n                                                                disabled: isDeletingKey === key.id,\n                                                                className: \"p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50\",\n                                                                \"data-tooltip-id\": \"tooltip-edit-\".concat(key.id),\n                                                                \"data-tooltip-content\": \"Edit Model & Settings\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1536,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                                                        id: \"tooltip-edit-\".concat(key.id),\n                                                                        place: \"top\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1537,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1529,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setEditingRolesApiKey(key),\n                                                                disabled: isDeletingKey === key.id,\n                                                                className: \"p-2 text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors disabled:opacity-50\",\n                                                                \"data-tooltip-id\": \"tooltip-roles-\".concat(key.id),\n                                                                \"data-tooltip-content\": \"Manage Roles\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1546,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                                                        id: \"tooltip-roles-\".concat(key.id),\n                                                                        place: \"top\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1547,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1539,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDeleteKey(key.id, key.label),\n                                                                disabled: isDeletingKey === key.id,\n                                                                className: \"p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50\",\n                                                                \"data-tooltip-id\": \"tooltip-delete-\".concat(key.id),\n                                                                \"data-tooltip-content\": \"Delete Key\",\n                                                                children: [\n                                                                    isDeletingKey === key.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4 animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1557,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1559,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                                                        id: \"tooltip-delete-\".concat(key.id),\n                                                                        place: \"top\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1561,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1549,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1528,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1481,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, key.id, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1480,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1478,\n                                    columnNumber: 17\n                                }, this),\n                                !isLoadingKeysAndRoles && error && !error.startsWith(\"Error loading model configuration:\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-xl p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-5 w-5 text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1573,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-800 font-medium text-sm\",\n                                                children: [\n                                                    \"Could not load API keys/roles: \",\n                                                    error\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1574,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1572,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1571,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1449,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1448,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                lineNumber: 1281,\n                columnNumber: 9\n            }, this),\n            editingRolesApiKey && renderManageRolesModal(),\n            editingApiKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card w-full max-w-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Edit API Key\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1591,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setEditingApiKey(null),\n                                    className: \"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1596,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1592,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1590,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: editingApiKey.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1602,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                \"Current: \",\n                                                editingApiKey.provider,\n                                                \" (\",\n                                                editingApiKey.predefined_model_id,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1603,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1601,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Provider\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1610,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full p-2.5 bg-gray-50 border border-gray-300 rounded-md text-gray-700\",\n                                                    children: ((_llmProviders_find = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.find((p)=>p.id === editingApiKey.provider)) === null || _llmProviders_find === void 0 ? void 0 : _llmProviders_find.name) || editingApiKey.provider\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1613,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                    children: \"Provider cannot be changed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1616,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1609,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"editModelId\",\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Model\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1620,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"editModelId\",\n                                                    value: editPredefinedModelId,\n                                                    onChange: (e)=>setEditPredefinedModelId(e.target.value),\n                                                    disabled: !editModelOptions.length,\n                                                    className: \"w-full p-2.5 bg-white border border-gray-300 rounded-md text-gray-900 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-100\",\n                                                    children: editModelOptions.length > 0 ? editModelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: option.value,\n                                                            children: option.label\n                                                        }, option.value, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1632,\n                                                            columnNumber: 25\n                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        disabled: true,\n                                                        children: isFetchingProviderModels ? 'Loading models...' : 'No models available'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1637,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1623,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1619,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"editTemperature\",\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: [\n                                                        \"Temperature: \",\n                                                        editTemperature\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1645,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    id: \"editTemperature\",\n                                                    min: \"0\",\n                                                    max: \"2\",\n                                                    step: \"0.1\",\n                                                    value: editTemperature,\n                                                    onChange: (e)=>setEditTemperature(parseFloat(e.target.value)),\n                                                    className: \"slider-orange w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1648,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"0.0 (Focused)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1659,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"1.0 (Balanced)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1660,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"2.0 (Creative)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1661,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1658,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1644,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-lg p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600\",\n                                                children: \"You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1666,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1665,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1608,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1600,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setEditingApiKey(null),\n                                        className: \"btn-secondary\",\n                                        disabled: isSavingEdit,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1675,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSaveEdit,\n                                        disabled: isSavingEdit,\n                                        className: \"btn-primary\",\n                                        children: isSavingEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1689,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Saving...\"\n                                            ]\n                                        }, void 0, true) : 'Save Changes'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1682,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1674,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1673,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1589,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                lineNumber: 1588,\n                columnNumber: 9\n            }, this),\n            !configDetails && !isLoadingConfig && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            className: \"h-8 w-8 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1705,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1704,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-3\",\n                        children: \"Model Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1707,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 mb-8\",\n                        children: \"This API Model configuration could not be found or may have been deleted.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1708,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>navigateOptimistically('/my-models'),\n                        className: \"inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1713,\n                                columnNumber: 13\n                            }, this),\n                            \"Return to My API Models\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1709,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                lineNumber: 1703,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: confirmation.isOpen,\n                onClose: confirmation.hideConfirmation,\n                onConfirm: confirmation.onConfirm,\n                title: confirmation.title,\n                message: confirmation.message,\n                confirmText: confirmation.confirmText,\n                cancelText: confirmation.cancelText,\n                type: confirmation.type,\n                isLoading: confirmation.isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                lineNumber: 1720,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                id: \"global-tooltip\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                lineNumber: 1732,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n        lineNumber: 1144,\n        columnNumber: 5\n    }, this);\n}\n_s(ConfigDetailsPage, \"w95SNLgIaArO1YrBx/V9CLqbCSA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_7__.useConfirmation,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_11__.useNavigationSafe,\n        _hooks_useManageKeysPrefetch__WEBPACK_IMPORTED_MODULE_8__.useManageKeysPrefetch,\n        _hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_10__.useRoutingSetupPrefetch\n    ];\n});\n_c2 = ConfigDetailsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PROVIDER_OPTIONS$llmProviders.map\");\n$RefreshReg$(_c1, \"PROVIDER_OPTIONS\");\n$RefreshReg$(_c2, \"ConfigDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/my-models/[configId]/page.tsx\n"));

/***/ })

});