"""
RouKey Browser Automation Service
FastAPI microservice for browser automation with LangGraph orchestration
"""

import asyncio
import logging
import os
import sys
from contextlib import asynccontextmanager
from typing import Dict, Any

# Fix for Windows asyncio issue with <PERSON>wright - must be set before any async operations
if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

import uvicorn
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from dotenv import load_dotenv

from app.core.config import settings
from app.core.logging import setup_logging
from app.api.routes import browser_automation, health, test
from app.services.session_manager import SessionManager
from app.services.browser_pool import BrowserPool
from app.core.exceptions import setup_exception_handlers

# Load environment variables
load_dotenv()

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("🚀 Starting RouKey Browser Automation Service")
    
    # Initialize services
    try:
        # Initialize browser pool (with fallback for Windows Playwright issues)
        try:
            browser_pool = BrowserPool()
            await browser_pool.initialize()
            app.state.browser_pool = browser_pool
            logger.info("✅ Browser pool initialized successfully")
        except Exception as browser_error:
            logger.warning(f"⚠️ Browser pool initialization failed: {browser_error}")
            logger.info("🔄 Service will run in test mode without browser pool")
            app.state.browser_pool = None

        # Initialize session manager
        session_manager = SessionManager()
        await session_manager.initialize()
        app.state.session_manager = session_manager

        logger.info("✅ Core services initialized successfully")

    except Exception as e:
        logger.error(f"❌ Failed to initialize core services: {e}")
        raise
    
    yield
    
    # Cleanup
    logger.info("🔄 Shutting down services")
    try:
        if hasattr(app.state, 'browser_pool'):
            await app.state.browser_pool.cleanup()
        if hasattr(app.state, 'session_manager'):
            await app.state.session_manager.cleanup()
        logger.info("✅ Cleanup completed")
    except Exception as e:
        logger.error(f"❌ Error during cleanup: {e}")


# Create FastAPI application
app = FastAPI(
    title="RouKey Browser Automation Service",
    description="Advanced browser automation with LangGraph orchestration and intelligent role routing",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Setup exception handlers
setup_exception_handlers(app)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.DEBUG else ["localhost", "127.0.0.1"]
)

# Include routers
app.include_router(health.router, prefix="/health", tags=["health"])
app.include_router(browser_automation.router, prefix="/api/v1/browser", tags=["browser"])
app.include_router(test.router, prefix="/api/v1/test", tags=["test"])


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "RouKey Browser Automation Service",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs" if settings.DEBUG else "disabled"
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.SERVICE_HOST,
        port=settings.SERVICE_PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
        workers=1 if settings.DEBUG else settings.MAX_WORKERS
    )
