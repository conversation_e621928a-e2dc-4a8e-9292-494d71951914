import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const authCookie = cookieStore.get('sb-access-token');
    
    if (!authCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from auth cookie
    const { data: { user }, error: authError } = await supabase.auth.getUser(authCookie.value);
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user tier
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('tier')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    const userTier = subscription?.tier || 'free';

    // Define tier limits
    const tierLimits = {
      free: 0,
      starter: 15,
      pro: 100,
      professional: 100, // Same as pro tier
      enterprise: 1000
    };

    const monthlyLimit = tierLimits[userTier as keyof typeof tierLimits] || 0;

    // Get current month usage
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format
    
    let { data: usage, error: usageError } = await supabase
      .from('browser_automation_usage')
      .select('*')
      .eq('user_id', user.id)
      .eq('month_year', currentMonth)
      .single();

    if (usageError && usageError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking browser automation usage:', usageError);
      return NextResponse.json({ error: 'Failed to check usage quota' }, { status: 500 });
    }

    if (!usage) {
      // Create new usage record
      const { data: newUsage, error: createError } = await supabase
        .from('browser_automation_usage')
        .insert({
          user_id: user.id,
          month_year: currentMonth,
          tier: userTier,
          tasks_used: 0,
          tasks_limit: monthlyLimit
        })
        .select()
        .single();

      if (createError) {
        console.error('Error creating usage record:', createError);
        return NextResponse.json({ error: 'Failed to initialize usage tracking' }, { status: 500 });
      }
      usage = newUsage;
    }

    // Get recent tasks for this month
    const { data: recentTasks, error: tasksError } = await supabase
      .from('browser_automation_tasks')
      .select('id, task_description, task_type, status, created_at, execution_time_seconds, success')
      .eq('user_id', user.id)
      .gte('created_at', `${currentMonth}-01`)
      .order('created_at', { ascending: false })
      .limit(10);

    if (tasksError) {
      console.error('Error fetching recent tasks:', tasksError);
    }

    // Calculate usage statistics
    const tasksUsed = usage.tasks_used;
    const tasksRemaining = Math.max(0, usage.tasks_limit - tasksUsed);
    const usagePercentage = usage.tasks_limit > 0 ? (tasksUsed / usage.tasks_limit) * 100 : 0;
    const canExecute = tasksUsed < usage.tasks_limit;

    // Determine next tier benefits
    const nextTierBenefits = {
      free: { tier: 'starter', limit: 15, price: '$19/month' },
      starter: { tier: 'pro', limit: 100, price: '$49/month' },
      pro: { tier: 'enterprise', limit: 1000, price: 'Contact us' },
      enterprise: null
    };

    const nextTier = nextTierBenefits[userTier as keyof typeof nextTierBenefits];

    return NextResponse.json({
      user_tier: userTier,
      current_month: currentMonth,
      quota: {
        tasks_used: tasksUsed,
        tasks_limit: usage.tasks_limit,
        tasks_remaining: tasksRemaining,
        usage_percentage: Math.round(usagePercentage),
        can_execute: canExecute,
        last_task_at: usage.last_task_at
      },
      recent_tasks: recentTasks || [],
      tier_info: {
        current: {
          name: userTier,
          limit: usage.tasks_limit,
          features: userTier === 'free' ? [] : [
            'Browser automation',
            userTier === 'pro' || userTier === 'enterprise' ? 'Unlimited tasks' : `${usage.tasks_limit} tasks/month`,
            userTier === 'enterprise' ? 'Priority support' : 'Standard support'
          ]
        },
        next_tier: nextTier
      },
      warnings: [
        ...(usagePercentage >= 90 ? ['You are approaching your monthly limit'] : []),
        ...(usagePercentage >= 100 ? ['Monthly quota exceeded - upgrade to continue'] : []),
        ...(userTier === 'free' ? ['Upgrade to Starter plan to enable browser automation'] : [])
      ]
    });

  } catch (error) {
    console.error('Error checking browser automation quota:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
